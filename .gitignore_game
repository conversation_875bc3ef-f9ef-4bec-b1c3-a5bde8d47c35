# .gitignore_game - 部署模式配置
# 只上传 dist 文件夹内的文件到 GitHub 仓库根目录，不包含 dist 文件夹本身
# 项目结构保持不变，dist 文件夹保留在原位置

# ==========================================
# 忽略所有文件，然后只允许 dist 内的内容
# ==========================================

# 忽略根目录下的所有文件和文件夹
/*

# 忽略所有隐藏文件
.*

# ==========================================
# 只允许 dist 文件夹内的内容
# ==========================================

# 允许 dist 文件夹本身被访问（但不上传）
!dist/

# 允许 dist 文件夹内的所有内容
!dist/**

# ==========================================
# 使用说明
# ==========================================

# 此配置的工作原理：
# 1. 使用 /* 忽略根目录下的所有文件和文件夹
# 2. 使用 !dist/ 允许访问 dist 文件夹
# 3. 使用 !dist/** 允许 dist 文件夹内的所有内容被上传
# 4. Git 会将 dist 内的文件结构映射到仓库根目录

# 部署步骤：
# 1. 更新源代码内容
# 2. 运行构建命令：npm run build
# 3. 手动替换 .gitignore 内容：将此文件内容复制到 .gitignore
# 4. 提交并推送：git add . && git commit -m "Update game content" && git push

# 推送后的 GitHub 仓库结构：
# 仓库根目录/
# ├── index.html          (来自 dist/index.html)
# ├── css/               (来自 dist/css/)
# ├── js/                (来自 dist/js/)
# ├── images/            (来自 dist/images/)
# ├── popular/           (来自 dist/popular/)
# ├── new/               (来自 dist/new/)
# └── 其他静态文件...      (来自 dist/ 下的其他文件)

# 注意：
# - 此配置适用于专门的部署仓库
# - 项目结构保持不变，dist 文件夹始终存在
# - 每次 build 后直接 git push 即可部署
