# 语言包目录

此目录包含项目的国际化语言包文件。

## 文件结构

- `en.json` - 英语语言包（默认语言）
- `zh.json` - 中文语言包

## 语言包结构规范

每个语言包文件包含以下部分：

1. **meta** - 语言元信息
2. **seo** - SEO相关文本
3. **nav** - 导航菜单文本
4. **buttons** - 按钮文本
5. **search** - 搜索相关文本
6. **footer** - 页脚文本
7. **pages** - 页面内容文本
8. **faq** - FAQ内容
9. **games** - 游戏数据

## 占位符命名规范

- 导航相关：`{{I18N_NAV_*}}`
- 按钮相关：`{{I18N_BUTTON_*}}`
- 搜索相关：`{{I18N_SEARCH_*}}`
- 页脚相关：`{{I18N_FOOTER_*}}`
- 页面相关：`{{I18N_PAGE_*}}`

## 使用说明

1. 修改语言包文件后需要重新构建项目
2. 保持所有语言包的结构一致
3. 新增文本时需要同时更新所有语言包