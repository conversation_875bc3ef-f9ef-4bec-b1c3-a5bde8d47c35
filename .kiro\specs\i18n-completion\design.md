# Design Document

## Overview

This design document outlines the completion of the internationalization (i18n) system for the Bloodmoney Game Portal. The system already has a solid foundation with language packs, build script modifications, and basic template support. This design focuses on completing the remaining components to achieve full i18n functionality.

## Architecture

### Current State Analysis

The project currently has:
- ✅ Language pack system (`locales/en.json`, `locales/zh.json`)
- ✅ Multi-language build script with path handling
- ✅ Basic template internationalization
- ✅ Multi-language sitemap generation
- ✅ Search functionality with language-specific data files

### Missing Components

1. **Language Switcher UI Component** - Not fully integrated into templates
2. **Hreflang SEO Tags** - Not implemented in page generation
3. **Game Card Component** - Needs path and content internationalization
4. **Template Completion** - Some hardcoded text remains
5. **Documentation Updates** - README and maintenance guides need updates

## Components and Interfaces

### 1. Language Switcher Component

#### HTML Structure
```html
<div class="language-switcher">
  <button class="lang-btn" onclick="toggleLanguageMenu()">
    <span class="current-lang">{{CURRENT_LANG_LABEL}}</span>
    <span class="lang-arrow">▼</span>
  </button>
  <div class="lang-menu" id="langMenu">
    {{LANGUAGE_SWITCHER_OPTIONS}}
  </div>
</div>
```

#### Build Script Integration
- Generate `{{CURRENT_LANG_LABEL}}` based on current language
- Generate `{{LANGUAGE_SWITCHER_OPTIONS}}` with proper URLs for each language
- Handle relative path calculation for different page depths

#### CSS Styling
- Responsive design for mobile and desktop
- Consistent with existing theme system
- Proper z-index for dropdown menu
- Hover and active states

### 2. Hreflang SEO Implementation

#### Tag Generation Logic
```html
<link rel="alternate" hreflang="en" href="{{BASE_URL}}/{{ENGLISH_PATH}}" />
<link rel="alternate" hreflang="zh" href="{{BASE_URL}}/zh/{{CHINESE_PATH}}" />
<link rel="alternate" hreflang="x-default" href="{{BASE_URL}}/{{ENGLISH_PATH}}" />
```

#### URL Mapping Strategy
- Homepage: `/` (en) ↔ `/zh/` (zh)
- Popular: `/popular/` (en) ↔ `/zh/popular/` (zh)
- New Games: `/new/` (en) ↔ `/zh/new/` (zh)
- Game Details: `/popular_games/{slug}.html` (en) ↔ `/zh/popular_games/{slug}.html` (zh)

#### Build Script Integration
- Add `generateHreflangTags()` function
- Calculate equivalent URLs for each page type
- Handle base URL configuration from config.json

### 3. Template System Completion

#### Template Updates Required
1. **_main-layout-template.html**
   - Add language switcher component
   - Add hreflang tags placeholder
   - Ensure all text uses i18n placeholders

2. **game-detail-template.html**
   - Add language switcher component
   - Add hreflang tags placeholder
   - Verify relative path handling

3. **_list-layout-template.html**
   - Add language switcher component
   - Add hreflang tags placeholder
   - Complete i18n placeholder replacement

4. **404.html**
   - Add language switcher component
   - Add hreflang tags placeholder
   - Ensure error messages are internationalized

#### Placeholder System
- `{{LANGUAGE_SWITCHER}}` - Complete language switcher component
- `{{HREFLANG_TAGS}}` - SEO hreflang link tags
- `{{CURRENT_LANG_CODE}}` - Language code for HTML lang attribute
- `{{CURRENT_LANG_LABEL}}` - Display name for current language

### 4. Game Card Component Enhancement

#### Path Resolution
- Ensure game card links work correctly from different directory depths
- Handle relative paths for both English (root) and Chinese (/zh/) versions
- Maintain consistency with existing URL structure

#### Content Internationalization
- Game names from language pack
- Consistent link generation
- Proper image path handling

## Data Models

### Language Pack Structure (Already Implemented)
```json
{
  "meta": {
    "language": "en",
    "label": "English",
    "code": "en"
  },
  "ui": {
    "nav": {...},
    "buttons": {...},
    "search": {...},
    "footer": {...}
  },
  "seo": {...},
  "pages": {...},
  "games": {...}
}
```

### Build Configuration Enhancement
```json
{
  "languages": {
    "default": "en",
    "supported": ["en", "zh"]
  },
  "baseUrl": "https://example.com",
  "i18n": {
    "hreflang": true,
    "languageSwitcher": true
  }
}
```

## Error Handling

### Build Process Error Handling
1. **Missing Language Pack**: Graceful fallback to English
2. **Invalid Template Placeholders**: Clear error messages with line numbers
3. **URL Generation Errors**: Validation and fallback mechanisms
4. **Hreflang Generation**: Validate URLs before output

### Runtime Error Handling
1. **Language Switcher Failures**: Fallback to page reload
2. **Missing Translations**: Display key name as fallback
3. **Path Resolution Issues**: Graceful degradation to English version

## Testing Strategy

### Unit Testing
1. **Language Pack Loading**: Verify JSON parsing and structure
2. **URL Generation**: Test path calculation for all page types
3. **Template Processing**: Verify placeholder replacement
4. **Hreflang Generation**: Validate tag format and URLs

### Integration Testing
1. **Build Process**: Full build with both languages
2. **Page Generation**: Verify all pages generate correctly
3. **Link Validation**: Check all internal links work
4. **SEO Validation**: Verify hreflang implementation

### User Acceptance Testing
1. **Language Switching**: Test from all page types
2. **Content Verification**: Ensure all text is translated
3. **Mobile Responsiveness**: Test language switcher on mobile
4. **Browser Compatibility**: Test across major browsers

### Performance Testing
1. **Build Time**: Measure impact of i18n on build performance
2. **Page Load**: Ensure no performance regression
3. **Resource Loading**: Verify efficient CSS/JS loading

## Implementation Phases

### Phase 1: Language Switcher Integration
- Update all templates with language switcher component
- Implement build script logic for switcher generation
- Add CSS styling for the component

### Phase 2: Hreflang SEO Implementation
- Add hreflang tag generation to build script
- Update templates with hreflang placeholder
- Implement URL mapping logic

### Phase 3: Template System Completion
- Review and update all templates for remaining hardcoded text
- Ensure consistent i18n placeholder usage
- Verify HTML lang attribute handling

### Phase 4: Game Card Component Enhancement
- Update game card template for proper internationalization
- Ensure path resolution works correctly
- Test link generation from different page depths

### Phase 5: Documentation and Testing
- Update README with i18n features
- Create translation maintenance guide
- Comprehensive testing and validation